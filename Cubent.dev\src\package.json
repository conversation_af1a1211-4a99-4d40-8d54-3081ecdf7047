{"name": "cubent", "displayName": "%extension.displayName%", "description": "%extension.description%", "publisher": "cubent", "version": "0.30.1", "icon": "assets/icons/icon.png", "galleryBanner": {"color": "#617A91", "theme": "dark"}, "engines": {"vscode": "^1.84.0", "node": "20.19.2"}, "author": {"name": "cubent"}, "repository": {"type": "git", "url": "https://github.com/LaxBloxBoy2/cubent-extension.git"}, "homepage": "https://github.com/LaxBloxBoy2/cubent-extension", "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Education", "Snippets", "Testing"], "keywords": ["cline", "claude", "dev", "mcp", "openrouter", "coding", "agent", "autonomous", "chatgpt", "sonnet", "ai", "llama", "cubent", "cubent.dev"], "activationEvents": ["onLanguage", "onStartupFinished"], "main": "./dist/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "cubent-ActivityBar", "title": "%views.activitybar.title%", "icon": "assets/icons/icon.svg"}]}, "views": {"cubent-ActivityBar": [{"type": "webview", "id": "cubent.SidebarProvider", "name": "%views.sidebar.name%"}]}, "commands": [{"command": "cubent.plusButtonClicked", "title": "%command.newTask.title%", "icon": "$(add)"}, {"command": "cubent.mcpButtonClicked", "title": "%command.mcpServers.title%", "icon": "$(server)"}, {"command": "cubent.promptsButtonClicked", "title": "%command.prompts.title%", "icon": "$(organization)"}, {"command": "cubent.historyButtonClicked", "title": "%command.history.title%", "icon": "$(history)"}, {"command": "cubent.popoutButtonClicked", "title": "%command.openInEditor.title%", "icon": "$(link-external)"}, {"command": "cubent.settingsButtonClicked", "title": "%command.settings.title%", "icon": "$(settings-gear)"}, {"command": "cubent.toggleAutoApprove", "title": "OFF", "icon": "$(circle-slash)"}, {"command": "cubent.openInNewTab", "title": "%command.openInNewTab.title%", "category": "%configuration.title%"}, {"command": "cubent.explainCode", "title": "%command.explainCode.title%", "category": "%configuration.title%"}, {"command": "cubent.fixCode", "title": "%command.fixCode.title%", "category": "%configuration.title%"}, {"command": "cubent.improveCode", "title": "%command.improveCode.title%", "category": "%configuration.title%"}, {"command": "cubent.addToContext", "title": "%command.addToContext.title%", "category": "%configuration.title%"}, {"command": "cubent.newTask", "title": "%command.newTask.title%", "category": "%configuration.title%"}, {"command": "cubent.terminalAddToContext", "title": "%command.terminal.addToContext.title%", "category": "Terminal"}, {"command": "cubent.terminalFixCommand", "title": "%command.terminal.fixCommand.title%", "category": "Terminal"}, {"command": "cubent.terminalExplainCommand", "title": "%command.terminal.explainCommand.title%", "category": "Terminal"}, {"command": "cubent.setCustomStoragePath", "title": "%command.setCustomStoragePath.title%", "category": "%configuration.title%"}, {"command": "cubent.focusInput", "title": "%command.focusInput.title%", "category": "%configuration.title%"}, {"command": "cubent.acceptInput", "title": "%command.acceptInput.title%", "category": "%configuration.title%"}], "menus": {"editor/context": [{"submenu": "cubent.contextMenu", "group": "navigation"}], "cubent.contextMenu": [{"command": "cubent.addToContext", "group": "1_actions@1"}, {"command": "cubent.explainCode", "group": "1_actions@2"}, {"command": "cubent.improveCode", "group": "1_actions@3"}], "terminal/context": [{"submenu": "cubent.terminalMenu", "group": "navigation"}], "cubent.terminalMenu": [{"command": "cubent.terminalAddToContext", "group": "1_actions@1"}, {"command": "cubent.terminalFixCommand", "group": "1_actions@2"}, {"command": "cubent.terminalExplainCommand", "group": "1_actions@3"}], "view/title": [{"command": "cubent.promptsButtonClicked", "group": "navigation@1", "when": "view == cubent.SidebarProvider"}, {"command": "cubent.settingsButtonClicked", "group": "navigation@7", "when": "view == cubent.SidebarProvider"}], "editor/title": [{"command": "cubent.promptsButtonClicked", "group": "navigation@1", "when": "activeWebviewPanelId == cubent.TabPanelProvider"}, {"command": "cubent.settingsButtonClicked", "group": "navigation@7", "when": "activeWebviewPanelId == cubent.TabPanelProvider"}]}, "submenus": [{"id": "cubent.contextMenu", "label": "%views.contextMenu.label%"}, {"id": "cubent.terminalMenu", "label": "%views.terminalMenu.label%"}], "configuration": {"title": "%configuration.title%", "properties": {"cubent.allowedCommands": {"type": "array", "items": {"type": "string"}, "default": ["*"], "description": "%commands.allowedCommands.description%"}, "cubent.vsCodeLmModelSelector": {"type": "object", "properties": {"vendor": {"type": "string", "description": "%settings.vsCodeLmModelSelector.vendor.description%"}, "family": {"type": "string", "description": "%settings.vsCodeLmModelSelector.family.description%"}}, "description": "%settings.vsCodeLmModelSelector.description%"}, "cubent.customStoragePath": {"type": "string", "default": "", "description": "%settings.customStoragePath.description%"}, "cubent.cubentCloudEnabled": {"type": "boolean", "default": false, "description": "%settings.cubentCoderCloudEnabled.description%"}, "cubent.database.neon.url": {"type": "string", "default": "", "description": "Neon database connection URL"}, "cubent.database.supabase.url": {"type": "string", "default": "", "description": "Supabase project URL"}, "cubent.database.supabase.anonkey": {"type": "string", "default": "", "description": "Supabase anonymous key"}, "cubent.analytics.posthog.apikey": {"type": "string", "default": "", "description": "PostHog API key for analytics"}, "cubent.analytics.posthog.host": {"type": "string", "default": "https://app.posthog.com", "description": "PostHog host URL"}, "cubent.auth.oauth.clientid": {"type": "string", "default": "", "description": "OAuth client ID for authentication"}, "cubent.auth.device.baseurl": {"type": "string", "default": "https://app.cubent.dev", "description": "Device authentication base URL"}, "cubent.features.usage.tracking": {"type": "boolean", "default": true, "description": "Enable usage tracking"}, "cubent.features.trial.management": {"type": "boolean", "default": true, "description": "Enable trial management"}, "cubent.features.cubent.units.limit": {"type": "number", "default": 50, "description": "Default Cubent Units limit"}, "cubent.authState": {"type": "object", "default": null, "description": "Stored authentication state (internal use)"}, "cubent.session": {"type": "string", "default": "", "description": "Stored user session data (internal use)"}, "cubent.mcp.enabled": {"type": "boolean", "default": false, "description": "Enable Model Context Protocol (MCP) to access external tools and services"}, "cubent.mcp.serverCreationEnabled": {"type": "boolean", "default": false, "description": "Allow Cubent to create new MCP servers on demand"}, "cubent.mcp.alwaysAllow": {"type": "boolean", "default": false, "description": "Always allow MCP tool usage without prompting"}, "cubent.autocomplete.enabled": {"type": "boolean", "default": false, "description": "Enable Cubent autocomplete feature"}, "cubent.autocomplete.model": {"type": "string", "default": "codestral", "description": "Autocomplete model"}}}}, "scripts": {"lint": "eslint . --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "pretest": "turbo run bundle --cwd ..", "test": "jest -w=40% && vitest run", "format": "prettier --write .", "bundle": "node esbuild.mjs", "vscode:prepublish": "npm run bundle", "vsix": "mkdirp ../bin && npx vsce package --no-dependencies --out ../bin", "publish:marketplace": "vsce publish --no-dependencies && ovsx publish --no-dependencies", "watch:bundle": "pnpm bundle --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "clean": "rimraf README.md CHANGELOG.md LICENSE dist webview-ui out mock .turbo"}, "dependencies": {"@anthropic-ai/bedrock-sdk": "^0.10.2", "@anthropic-ai/sdk": "^0.37.0", "@anthropic-ai/vertex-sdk": "^0.7.0", "@aws-sdk/client-bedrock-runtime": "^3.779.0", "@aws-sdk/credential-providers": "^3.806.0", "@cubent/cloud": "workspace:^", "@cubent/telemetry": "workspace:^", "@cubent/types": "workspace:^", "@google/genai": "^0.13.0", "@mistralai/mistralai": "^1.3.6", "@modelcontextprotocol/sdk": "^1.9.0", "@neondatabase/serverless": "^0.6.0", "@prisma/adapter-neon": "^6.11.1", "@prisma/client": "^6.11.1", "@qdrant/js-client-rest": "^1.14.0", "@supabase/supabase-js": "^2.38.0", "@types/lodash.debounce": "^4.0.9", "@vscode/codicons": "^0.0.36", "async-mutex": "^0.5.0", "axios": "^1.7.4", "cheerio": "^1.0.0", "chokidar": "^4.0.1", "clone-deep": "^4.0.1", "date-fns": "^2.30.0", "default-shell": "^2.2.0", "delay": "^6.0.0", "diff": "^5.2.0", "diff-match-patch": "^1.0.5", "fast-deep-equal": "^3.1.3", "fast-xml-parser": "^4.5.1", "fastest-levenshtein": "^1.0.16", "fzf": "^0.5.2", "get-folder-size": "^5.0.0", "google-auth-library": "^9.15.1", "i18next": "^24.2.2", "ignore": "^7.0.3", "isbinaryfile": "^5.0.2", "lodash.debounce": "^4.0.8", "mammoth": "^1.8.0", "monaco-vscode-textmate-theme-converter": "^0.1.7", "node-cache": "^5.1.2", "node-ipc": "^12.0.0", "openai": "^4.78.1", "os-name": "^6.0.0", "p-limit": "^6.2.0", "p-wait-for": "^5.0.2", "pdf-parse": "^1.1.1", "pkce-challenge": "^4.1.0", "posthog-node": "^3.1.0", "pretty-bytes": "^6.1.1", "prisma": "^6.11.1", "ps-tree": "^1.2.0", "puppeteer-chromium-resolver": "^23.0.0", "puppeteer-core": "^23.4.0", "reconnecting-eventsource": "^1.6.4", "sanitize-filename": "^1.6.3", "say": "^0.16.0", "serialize-error": "^11.0.3", "simple-git": "^3.27.0", "sound-play": "^1.1.0", "string-similarity": "^4.0.4", "strip-ansi": "^7.1.0", "strip-bom": "^5.0.0", "tiktoken": "^1.0.21", "tmp": "^0.2.3", "tree-sitter-wasms": "^0.1.11", "turndown": "^7.2.0", "uuid": "^11.1.0", "vscode-material-icons": "^0.1.1", "web-tree-sitter": "^0.22.6", "workerpool": "^9.2.0", "yaml": "^2.8.0", "zod": "^3.24.2"}, "devDependencies": {"@cubent/build": "workspace:^", "@cubent/config-eslint": "workspace:^", "@cubent/config-typescript": "workspace:^", "@jest/globals": "^29.7.0", "@types/clone-deep": "^4.0.4", "@types/debug": "^4.1.12", "@types/diff": "^5.2.1", "@types/diff-match-patch": "^1.0.36", "@types/glob": "^8.1.0", "@types/jest": "^29.5.14", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/node-cache": "^4.1.3", "@types/node-ipc": "^9.2.3", "@types/ps-tree": "^1.1.6", "@types/string-similarity": "^4.0.2", "@types/tmp": "^0.2.6", "@types/turndown": "^5.0.5", "@types/uuid": "^9.0.0", "@types/vscode": "^1.84.0", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "3.3.2", "esbuild": "^0.25.0", "execa": "^9.5.2", "glob": "^11.0.1", "jest": "^29.7.0", "jest-simple-dot-reporter": "^1.0.5", "mkdirp": "^3.0.1", "nock": "^14.0.4", "npm-run-all2": "^8.0.1", "ovsx": "0.10.2", "rimraf": "^6.0.1", "ts-jest": "^29.2.5", "tsup": "^8.4.0", "tsx": "^4.19.3", "typescript": "5.8.3", "vitest": "^3.1.3", "zod-to-ts": "^1.2.0"}}